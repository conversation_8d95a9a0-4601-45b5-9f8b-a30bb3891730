/*
 * @Author: your name
 * @Date: 2021-04-15 17:40:03
 * @LastEditTime: 2021-06-17 10:56:40
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: \ublinker-fe\ucf-apps\data\sync-task\src\routes\index.js
 */
import React, { Component, createContext } from "react";
import mixCore, { RoutesRender } from "core";
import { inject, observer } from "mobx-react";
import ListContainer from "./list/container";
import ColumnsContainer from "./columns/container";
import PublicDocContainer from "./public-doc/container";
import DataDetailContainer from "./data-detail/container";
import SyncLogContainer from "./sync-log/container";
import BatchLogContainer from "./batch-log/container";
import BatchLogDetailContainer from "./batch-detail/container";
import BatchMonitorContainer from "./batch-monitor/container";
// import BatchLogContainer from "../../../../task-monitoring/src/routes/batch-log/container";
// import BatchLogDetailContainer from "../../../../task-monitoring/src/routes/batch-detail/container";
// import BatchMonitorContainer from "../../../../task-monitoring/src/routes/batch-monitor/container";
import FailCompensateContainer from "./fail-compensate/container";
import getConfigProvider from "components/ConfigProvider";
import TimingContainer from "../../../timing/src/routes/list/container";
import IntegratedLog from "./integratedLog";

import config from "../config";
import CommonStore, { storeKey as commonStoreKey } from "./store";

mixCore.addStore({
    storeKey: commonStoreKey,
    store: new CommonStore(),
});

const ConfigProvider = getConfigProvider(config);

const routes = [
    {
        path: "/",
        exact: true,
        component: ListContainer,
    },
    {
        path: "/list",
        component: ListContainer,
    },
    {
        path: "/columns",
        component: ColumnsContainer,
    },
    {
        path: "/pub-doc",
        component: PublicDocContainer,
    },
    {
        path: "/data-detail",
        component: DataDetailContainer,
    },
    {
        path: "/sync-log",
        component: SyncLogContainer,
    },
    {
        path: "/batch-log",
        component: BatchLogContainer,
    },
    {
        path: "/batch-detail",
        component: BatchLogDetailContainer,
    },
    {
        path: "/batch-monitor",
        component: BatchMonitorContainer,
    },
    {
        path: "/fail-compensate",
        component: FailCompensateContainer,
    },
    {
        path: "/timing",
        component: TimingContainer,
    },
    {
        path: "/integratedLog/:id/:viewTag/:startTime/:endTime",
        component: IntegratedLog,
    },
];
const initData = {
    order: "asc",
    enable: "1",
    key: "",
    taskstatus: "",
    startTypeInt: "",
    gateWayId: "",
};

export const QueryContext = createContext({
    query: initData,
    updateQuery: () => {},
});
@inject((rootStore) => {
    let commonStore = rootStore[commonStoreKey];
    return {
        commonState: commonStore.toJS(),
        commonStore: commonStore,
    };
})
@observer
class Routes extends Component {
    constructor() {
        super();
        this.state = {
            query: initData,
        };
    }

    updateQuery = (newQuery) => {
        this.setState((prevState) => ({
            query: { ...newQuery }, // 创建新对象
        }));
    };

    render() {
        return (
            <QueryContext.Provider
                value={{
                    query: this.state.query,
                    updateQuery: this.updateQuery,
                    initData,
                }}
            >
                <ConfigProvider fieldid="UCG-FE-data-sync-task-src-routes-index-2742258-ConfigProvider">
                    <RoutesRender routes={routes.map((route) => ({ ...route, componentProps: { otherSource: this.props.otherSource } }))} />
                </ConfigProvider>
            </QueryContext.Provider>
        );
    }
}
export default Routes;
