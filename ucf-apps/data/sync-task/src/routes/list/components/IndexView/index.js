import React, { Component } from "react";
import { Space, Table, Clipboard, Menu, Message, Layout, Input, Modal, Button, Dropdown, Tree, Spin } from "@tinper/next-ui";
import queryString from "query-string";
import { navTo, withIframeSize, getPageParams } from "decorator/index";
import withRouter from "decorator/withRouter";
import DotSwitch from "components/DotSwitch";
import { Warning } from "utils/feedback";
import CheckModal from "../CheckModal";
import CheckLogModal from "../CheckLogModal";
import ConditionFilterModal from "./ConditionFilterModal";
import DispatchModal from "ucf-apps/data/components/DispatchModal";
import ViewManageModal from "../ViewManageModal";
import { autoServiceMessage } from "utils/service";
import { getCheckLogService, immediateExecuteService, getScheduleDetailService, updateScheduleService } from "../../service";
import SearchForm from "./SearchForm";

import Pagination from "components/TinperBee/Pagination";
import PageIntroduce from "components/PageIntroduce";
import CustomTag from "components/CustomTag";
import ResizeObserver from "resize-observer-polyfill";
const { dragColumn, multiSelect, sort } = Table;
const DragColumnTable = multiSelect(sort(Table));
import * as ownerService from "../../service";
import styles from "./index.modules.css";
import "./index.less";
import FlowScheduler from "ucf-apps/data/components/FlowScheduler";

const { Spliter } = Layout;

export const triggerModeMap = {
    get 1() {
        return lang.templateByUuid("UID:P_UBL-FE_1F7D965C05600018", "定时触发") /* "定时触发" */;
    },
    // 2: "API触发",
    get 3() {
        return lang.templateByUuid("UID:P_UBL-FE_1F7D965C0560001A", "YonBIP事件触发") /* "YonBIP事件触发" */;
    },
    get 10() {
        return lang.templateByUuid("UID:P_UBL-FE_1F7D965C0560001B", "API触发") /* "API触发" */;
    },
    // 11: "Kafka监听",
    // 12: "Redis订阅",
    // 13: "Email监听",
    // 14: "主数据监听",
};
export const taskStatusTypes = [
    {
        code: 0,
        name: lang.templateByUuid("UID:P_UBL-FE_18D8CEF6041804F5", "未运行") /* "未运行" */,
        cls: "default",
    },
    {
        code: 1,
        name: lang.templateByUuid("UID:P_UBL-FE_18D8CEF6041804E9", "运行中") /* "运行中" */,
        cls: "pending",
    },
    {
        code: 2,
        name: lang.templateByUuid("UID:P_UBL-FE_18D8CEF6041804EB", "运行成功") /* "运行成功" */,
        cls: "success",
    },
    {
        code: 3,
        name: lang.templateByUuid("UID:P_UBL-FE_18D8CEF6041804EE", "运行失败") /* "运行失败" */,
        cls: "fail",
    },
    {
        code: 4,
        name: lang.templateByUuid("UID:P_UBL-FE_18D8CEF6041804F0", "部分成功") /* "部分成功" */,
        cls: "warning",
    },
    {
        code: "5",
        name: lang.templateByUuid("UID:P_UBL-FE_18D8CEF6041804F1", "运行冲突") /* "运行冲突" */,
        cls: "default",
    },
    {
        code: "6",
        name: lang.templateByUuid("UID:P_UBL-FE_18D8CEF6041804F3", "跳过") /* "跳过" */,
        cls: "default",
    },
    {
        code: "7",
        name: lang.templateByUuid("UID:P_UBL-FE_18D8CEF6041804F4", "等待") /* "等待" */,
        cls: "default",
    },
    {
        code: "8",
        name: lang.templateByUuid("UID:P_UBL-FE_18D8CEF6041804E8", "数据处理中") /* "数据处理中" */,
        cls: "default",
    },
];
@withIframeSize
@withRouter
@getPageParams
class IndexView extends Component {
    constructor(props) {
        super(props);
        this.state = {
            sqlWhereShow: false,
            checkShow: false,
            checkLogShow: false,
            editViewShow: false,
            createTimingShow: false,
            initNcModalShow: false,
            dataMapShow: false,
            seeSqlShow: false,
            editDataMapShow: false,
            dataMakeUpShow: false,
            isTenantRes: {},
            logData: {},
            viewManageModalShow: false,
            isPull: false,
            guidStep: null,
            size: { width: 0, height: 0 },
            columns: [],
            selectedRowKeys: [],
            conditionFilter: { visible: false, data: null },
            treeWidth: 246,
            flowSchedulerData: { taskCode: "", taskId: "", visible: false },
        };
        this.searchInputRef = React.createRef();
    }
    dispatchModalRef = React.createRef();
    searchFormRef = React.createRef();
    bodyRef = React.createRef();
    tableRef = React.createRef();

    searchDataVars = this.props.queryContext?.query || {};
    // 提取常用按钮为方法，返回对应的按钮组件
    getDataDetailButton = (record) => (
        <Button fieldid="8b986e36-ae1f-4fda-96ea-4c68f68819d4" size="sm" colors="dark" onClick={this.navToDataDetail.bind(null, record)}>
            {lang.templateByUuid("UID:P_UBL-FE_18D8CEF6041801F1", "数据详情")}
        </Button>
    );

    getRunLogButton = (record) => (
        <Button fieldid="83a0ea5c-d0dd-443a-b792-faa0de0cccd7" size="sm" colors="dark" onClick={this.navOperRecord.bind(null, record)}>
            {lang.templateByUuid("UID:P_UBL-FE_18D8CEF6041801E2", "运行记录")}
        </Button>
    );

    getStopButton = (record) => {
        const { ownerStore } = this.props;
        return (
            <Button fieldid="7f9430bf-8f99-4346-b6ef-779da9626319" size="sm" colors="dark" onClick={this.handleStopTask.bind(null, record)}>
                {lang.templateByUuid("UID:P_UBL-FE_18D8CEF6041801DB", "停止")}
            </Button>
        );
    };

    getExecuteNowButton = (record) => {
        return (
            <Button
                fieldid="699518c6-4af7-48bf-8549-c3f5734ed9f0"
                size="sm"
                colors="dark"
                onClick={() => {
                    this.handleExecute.bind(null, record)();
                }}
            >
                {lang.templateByUuid("UID:P_UBL-FE_18D8CEF6041801DA", "立即执行")}
            </Button>
        );
    };

    getTimingExecuteButton = (record) => (
        <Button fieldid="70b0937f-84f0-4efc-9718-b5a4ced3f998" size="sm" colors="dark" onClick={() => this.handleOpenDispatchModal(record)}>
            {lang.templateByUuid("UID:P_UBL-FE_18D8CEF6041801DD", "定时执行")}
        </Button>
    );

    getCheckExecuteDropdown = (record) => (
        <Dropdown.Button
            fieldid="8f3e4fbe-756c-4b4e-813e-18671f05e5be"
            size="sm"
            colors="dark"
            overlay={this.viewCheckMenu(record)}
            type="dark"
            onClick={this.changeCheckShow.bind(null, record)}
        >
            {lang.templateByUuid("UID:P_UBL-FE_18D8CEF6041801E0", "排查执行")}
        </Dropdown.Button>
    );
    handleStopTask = async (record) => {
        const { stopTask, getDataSource } = this.props.ownerStore;
        const res = await stopTask(record);
        if (res) {
            getDataSource({ ...this.searchDataVars, pageNo: 1 });
        }
    };
    async componentDidMount() {
        //从升迁工具跳过来
        let pageParam2 = this.props.getPageParams(this.props); //接收手动点击返回的参数进行判断，否则点返回会再次跳转
        if (!pageParam2.queryParams.back) {
            if (this.props.appContext && this.props.appContext.currRouteParam.router) {
                this.props.navigate({
                    pathname: `/${this.props.appContext.currRouteParam.router}`,
                });
                return;
            }
        }

        const { getConnectorList, getTreeData, getDataSource } = this.props.ownerStore;
        try {
            this.searchFormRef.current?.setFieldsValue(this.searchDataVars);
            // 零代码
            if (this.props.otherSource?.source === 3) {
                this.props.ownerStore.setSelectedKeys([this.props.otherSource.parentId], this.props.otherSource.isRoot, false);
            }
            // 先加载基础数据
            await Promise.allSettled([getTreeData(), getConnectorList()]);
            // 再加载列表数据
            await getDataSource({ ...this.searchDataVars, pageNo: 1 });
        } catch (error) {
            console.log("Error fetching data:", error);
        }
        this.resizeObserver = new ResizeObserver((entries) => {
            entries.forEach((entry) => {
                const { clientWidth, clientHeight } = entry.target;
                this.setState({ size: { width: clientWidth, height: clientHeight } });
            });
        });
        if (this.bodyRef.current) {
            this.resizeObserver.observe(this.bodyRef.current);
        }
    }

    componentDidUpdate(prevProps) {
        if (prevProps.queryContext.query !== this.props.queryContext.query) {
            // 更新searchDataVars为最新的queryContext.query
            this.searchDataVars = this.props.queryContext.query;
            if (this.searchDataVars) {
                this.searchFormRef.current.setFieldsValue(this.searchDataVars);
            }
            this.props.ownerStore.getDataSource({ ...this.searchDataVars, pageNo: 1 });
        }
    }

    componentWillUnmount() {
        this.resizeObserver?.disconnect();
        // this.props.ownerStore.revertQuery();
    }

    renderStatus = (taskstatus1) => {
        const taskStatus = taskStatusTypes[taskstatus1];

        let styleNames = `${styles["status-tag"]}`;
        if (taskStatus && taskStatus.cls) {
            styleNames += ` ${styles[taskStatus.cls]}`;
        }
        return <CustomTag tagName={taskStatus.tagName || taskStatus.name} status={taskStatus?.cls} />;
    };

    renderChangeStatus = (enable, record, index) => {
        return <DotSwitch size="sm" checked={enable} onChange={this.handleSwitch.bind(null, record)} />;
    };

    /**
     * 显示或者隐藏排查执行modal
     */
    changeCheckShow = (task) => {
        this.props.ownerStore.onTaskGridSelect(task);
        this.setState({ checkShow: !this.state.checkShow });
    };
    /**
     * 显示或者隐藏排查日志modal
     */
    changeCheckLogShow = async (task) => {
        this.props.ownerStore.onTaskGridSelect(task);

        let res = await autoServiceMessage({
            service: getCheckLogService(task.pk_id, { serviceCode: this.props.ownerState.serviceCodeDiwork }),
        });
        if (res && res.status == 1) {
            this.setState({
                logData: res.data,
                checkLogShow: !this.state.checkLogShow,
            });
        }
    };
    changeCheckLogClose = () => {
        this.setState({
            checkLogShow: !this.state.checkLogShow,
        });
    };

    handleCheckOk = async (data) => {
        let { ownerStore } = this.props;
        let res = await ownerStore.checkOkFunc(data);
        if (res) {
            this.setState({ checkShow: !this.state.checkShow });
            Modal.success({
                title: lang.templateByUuid("UID:P_UBL-FE_18D8CEF6041801D3", "操作成功！") /* "操作成功！" */,
                fieldid: "************",
                content: (
                    <div>
                        <p>
                            {lang.templateByUuid("UID:P_UBL-FE_18D8CEF6041801D5", "将要执行的数据同步任务：", undefined, {
                                returnStr: true,
                            }) + res.data.viewNames || ""}
                        </p>
                    </div>
                ),
                confirmType: "one",
                okText: lang.templateByUuid("UID:P_UBL-FE_18D8CEF6041801D8", "查看执行详情") /* "查看执行详情" */,
                okCancel: true,
                onOk: () => {
                    this.props.navigate({
                        pathname: "/batch-detail",
                        search: `?batchId=${res.data.batchId}&fromType=${res.data.fromType}`,
                    });
                },
            });
        }
    };
    handleCheck = async (data, cb) => {
        let { ownerStore } = this.props;
        ownerStore.checkFunc(data, cb);
    };

    handleSwitch = async (record, checked) => {
        let { changeEnable } = this.props.ownerStore;
        const res = await changeEnable({ id: record.pk_id, enable: checked });
        if (res) {
            this.props.ownerStore.getDataSource({ ...this.searchDataVars, pageNo: 1 });
        }
    };

    viewCheckMenu = (task) => {
        return (
            <Menu fieldid="UCG-FE-routes-list-components-IndexView-index-7889318-Menu">
                {/* <Menu.Item fieldid="UCG-FE-routes-list-components-IndexView-index-1146307-Menu.Item" onClick={this.changeCheckShow.bind(null, task)}>
                    {lang.templateByUuid("UID:P_UBL-FE_18D8CEF6041801E0", "排查执行")}
                </Menu.Item> */}

                <Menu.Item
                    fieldid="UCG-FE-routes-list-components-IndexView-index-3327396-Menu.Item"
                    onClick={this.changeCheckLogShow.bind(null, task, "mapping")}
                >
                    {lang.templateByUuid("UID:P_UBL-FE_18D8CEF6041801F4", "排查日志") /* "排查日志" */}
                </Menu.Item>
            </Menu>
        );
    };

    /**
     * 执行任务
     * @param {Object} taskInfo -任务信息
     * @param {String} executeType=[now|where] -now立即执行 where按条件拉取
     */
    handleExecute = async (taskInfo) => {
        let { ownerStore } = this.props;
        let res = await ownerStore.checkRunnable(taskInfo.pk_id);
        if (res && res.status == 1) {
            this.setState({ conditionFilter: { visible: true, data: taskInfo } });
            return;
        } else {
            Modal.confirm({
                fieldid: "************",
                title: res.msg,
            });
        }
    };

    //运行记录
    navOperRecord = (task) => {
        if (task) {
            this.props.navigate({
                pathname: "/sync-log",
                search: "?" + queryString.stringify({ taskId: task.pk_id, dataTypeName: task.dataTypeName, fromType: task.fromType }),
            });
        }
    };

    /**
     * 显示或者隐藏创建定时任务接口
     */
    changeCreateTimingShow = (task) => {
        this.props.ownerStore.onTaskGridSelect(task);
        this.setState({ createTimingShow: !this.state.createTimingShow });
    };

    createTimingOk = async (cronexpression, radioVal, task, switchState) => {
        let { ownerStore } = this.props;
        if (radioVal == "1") {
            let res = await ownerStore.createTimingTask(cronexpression);
            if (res) {
                this.changeCreateTimingShow();
            }
        } else {
            if (task) {
                let content = "";
                //是：同步任务由平台预设定时任务执行，同步时间为每日凌晨1点至5点之间;否：同步任务需手动触发或单独创建定时任务。
                if (task.excluded) {
                    //当前为 否 将要切换成 是
                    content = lang.templateByUuid(
                        "UID:P_UBL-FE_18D8CEF6041801C9",
                        "切换成功后，同步任务由平台预设定时任务执行，同步时间为每日凌晨1点至5点之间" //@notranslate
                    ); /* "切换成功后，同步任务由平台预设定时任务执行，同步时间为每日凌晨1点至5点之间" */
                } else {
                    //当前为 是 将要切换成 否
                    content = lang.templateByUuid(
                        "UID:P_UBL-FE_18D8CEF6041801CA",
                        "切换成功后，同步任务需手动触发或单独创建定时任务" //@notranslate
                    ); /* "切换成功后，同步任务需手动触发或单独创建定时任务" */
                }
                Modal.confirm({
                    fieldid: "************",
                    title: lang.templateByUuid("UID:P_UBL-FE_18D8CEF6041801CD", "确认切换？") /* "确认切换？" */,
                    content: content,
                    onOk: ownerStore.toggleTaskSyncMode.bind(null, task, this.changeCreateTimingShow, switchState),
                });
            }
        }
    };

    toggleTaskSyncMode = (task) => {
        let { ownerStore } = this.props;
        if (task) {
            let content = "";
            //是：同步任务由平台预设定时任务执行，同步时间为每日凌晨1点至5点之间;否：同步任务需手动触发或单独创建定时任务。
            if (task.excluded) {
                //当前为 否 将要切换成 是
                content = lang.templateByUuid(
                    "UID:P_UBL-FE_18D8CEF6041801C9",
                    "切换成功后，同步任务由平台预设定时任务执行，同步时间为每日凌晨1点至5点之间" //@notranslate
                ); /* "切换成功后，同步任务由平台预设定时任务执行，同步时间为每日凌晨1点至5点之间" */
            } else {
                //当前为 是 将要切换成 否
                content = lang.templateByUuid(
                    "UID:P_UBL-FE_18D8CEF6041801CA",
                    "切换成功后，同步任务需手动触发或单独创建定时任务" //@notranslate
                ); /* "切换成功后，同步任务需手动触发或单独创建定时任务" */
            }
            Modal.confirm({
                fieldid: "************",
                title: lang.templateByUuid("UID:P_UBL-FE_18D8CEF6041801CD", "确认切换？") /* "确认切换？" */,
                content: content,
                onOk: ownerStore.toggleTaskSyncMode.bind(null, task),
            });
        }
    };

    navToPublicDoc = () => {
        let {
            ownerState: { selectedTasks },
        } = this.props;
        let task = selectedTasks[0];
        if (task) {
            let { pk_id, dataview } = task;
            if (dataview.commondoccode) {
                this.props.navigate({
                    pathname: "/pub-doc",
                    search: "?" + queryString.stringify({ taskId: pk_id }),
                });
            } else {
                Warning(lang.templateByUuid("UID:P_UBL-FE_18D8CEF6041801E9", "此任务未关联公共档案") /* "此任务未关联公共档案" */);
            }
        }
    };

    navToPublicDoc = (task) => {
        if (task) {
            let { pk_id, dataview } = task;
            if (dataview.commondoccode) {
                this.props.navigate({
                    pathname: "/pub-doc",
                    search: "?" + queryString.stringify({ taskId: pk_id }),
                });
            } else {
                Warning(lang.templateByUuid("UID:P_UBL-FE_18D8CEF6041801E9", "此任务未关联公共档案") /* "此任务未关联公共档案" */);
            }
        }
    };

    navToDataDetail = (task) => {
        if (task) {
            this.props.ownerStore.onTaskGridSelect(task);
            this.props.navigate({
                pathname: "/data-detail",
                search:
                    "?" +
                    queryString.stringify({
                        taskId: task.pk_id,
                        taskInfo: JSON.stringify(task),
                        fromType: task.fromType,
                        dataTypeName: task.dataTypeName,
                    }),
            });
        }
    };

    changeMapModalShow = (task) => {
        this.props.ownerStore.onTaskGridSelect(task);
        this.setState({ dataMapShow: !this.state.dataMapShow });
    };
    changeEditMapModalShow = (task) => {
        // this.props.ownerStore.onTaskGridSelect(task)
        this.setState({ editDataMapShow: !this.state.editDataMapShow });
    };

    dropdownTrigger = ["click"];
    satisfactionTime = (lastexectime) => {
        // debugger
        var time = new Date(lastexectime);
        var maxtime = new Date();
        time.setHours(time.getHours() + 2);
        return time > maxtime;
    };
    // 运行监控
    handleOperMonitor = (record) => {
        window.jDiwork?.openService("kflj_Taskmonitoring", { filter: JSON.stringify({ traceId: record.traceId }) });
    };

    hoverContent = (record = {}) => {
        if (record.fromType == 3) {
            // 集成流
            return (
                <Space fieldid="254dbdaa-df10-4b97-94c0-4285132df6d7" size={3}>
                    {/* 定时任务、API触发 有立即执行 */}
                    {[1].includes(record.startTypeInt) && record.taskstatus !== 1 && this.getExecuteNowButton(record)}
                    {/* 定时任务有定时执行 */}
                    {record.startTypeInt === 1 && this.getTimingExecuteButton(record)}
                    {record.taskstatus !== 1 && (
                        <Button fieldid="9e70c3a4-5266-42fc-9b6f-aa34bbe96dbd" size="sm" colors="dark" onClick={this.handleOperMonitor.bind(null, record)}>
                            {lang.templateByUuid("UID:P_UBL-FE_1F7D965C0560001D", "运行监控") /* "运行监控" */}
                        </Button>
                    )}

                    {this.getDataDetailButton(record)}
                    {this.getRunLogButton(record)}
                </Space>
            );
        }

        // 原视图的任务，操作保持不变
        if (record.getDataWay === 0) {
            //被动执行
            return (
                <Space fieldid="ec2e8f78-1663-46c1-921e-6e13da67a631" size={3}>
                    {this.getDataDetailButton(record)}
                    {this.getRunLogButton(record)}
                </Space>
            );
        }
        return (
            <Space fieldid="b9c6aea9-a57f-4cd9-bb34-df13696503b8" size={3}>
                {record.taskstatus === 1 || record.taskstatus === 8
                    ? (record.taskstatus !== 8 || !this.satisfactionTime(record.lastexectime)) && this.getStopButton(record)
                    : this.getExecuteNowButton(record)}
                {record.taskstatus !== 8 && this.getTimingExecuteButton(record)}
                {this.getDataDetailButton(record)}
                {record.dataview?.appcode !== "u8" && record.taskstatus !== 1 && record.taskstatus !== 8 && !record.fromType && (
                    <Dropdown.Button
                        fieldid="8f3e4fbe-756c-4b4e-813e-18671f05e5be"
                        size="sm"
                        colors="dark"
                        overlay={this.viewCheckMenu(record)}
                        type="dark"
                        onClick={this.changeCheckShow.bind(null, record)}
                    >
                        {lang.templateByUuid("UID:P_UBL-FE_18D8CEF6041801E0", "排查执行") /* "排查执行" */}
                    </Dropdown.Button>
                )}
                {this.getRunLogButton(record)}
            </Space>
        );
    };

    changeViewManageModalShow = (task) => {
        // fromType任务类型 0: 数据视图的方案  1 原有数据集成方案  2 使用底座数据的集成方案  3:集成流程设计器产生的同步任务
        if (task?.fromType === 3) {
            const jumpFlow = ((id, isAdd) => {
                const suffix = `#/designer`;
                const host = window._YMSHOST_ || "";
                const serviceCode = "/iuap-ipaas-base";
                const path = "/ucf-wh/designer-fe/index.html";
                const query = queryString.stringify({
                    flowId: id,
                    isAdd: isAdd,
                    locale: window.lang?.lang || "zh-CN",
                    from: "diwork",
                    source: this.props.otherSource?.source,
                    appCode: this.props.otherSource?.appCode,
                });
                if (window._YMSHOST_) {
                    // 避免被替换
                    console.warn("final", host + serviceCode + path + suffix);
                    // 线上
                    window.open(host + serviceCode + path + suffix + "?" + query);
                } else {
                    // 本地
                    window.open(host + serviceCode + path + suffix + "?" + query);
                }
            })(task.startSchemeId, false);
        } else if (task && (task.fromType == 1 || task.fromType == 2)) {
            window.jDiwork?.openService("kflj_jjfa", {
                routePath: task.fromType == 1 ? "/info" : "/applicationInfo",
                type: "edit",
                id: task.pkIntegratedId,
                from: "sync-task",
                providerHost: "/iuap-aip-ps/ucf-wh/js/",
            });
        } else {
            //老数据
            this.handleViewManageModalCommon(task);
        }
    };
    handleViewManageModalCommon = (task) => {
        //老数据
        this.props.ownerStore.onTaskGridSelect(task);
        task && task.changeConnect && this.props.ownerStore.onTaskGridSelectedGateway(task); //为了弹窗中的连接配置回显选中状态用  u8不调用
        this.setState({
            viewManageModalShow: !this.state.viewManageModalShow,
        });
    };
    handleCloseViewManageModal = (task) => {
        this.handleViewManageModalCommon(task);
        this.props.ownerStore.getDataSource({ ...this.searchDataVars, pageNo: 1 });
    };
    searchLabelCol = 100;
    columnsVar = [
        {
            title: lang.templateByUuid("UID:P_UBL-FE_18D8CEF6041801CF", "方案编码") /* "方案编码" */,
            dataIndex: "startSchemeCode",

            render: (text) => {
                return (
                    <span className={styles["code-render"]}>
                        <span>{text}</span>
                        <Clipboard fieldid="23fb8c42-c40d-47fa-8587-83793292e589" action="copy" text={text}>
                            <i
                                fieldid="1b733616-5b5f-41b5-94dd-d520d24afb2d"
                                className={`ipaas iPS-copy-public-line ${styles["code-copy"]}`}
                                title={lang.templateByUuid("UID:P_UBL-FE_1C081F0E05200028", "复制") /* "复制" */}
                            />
                        </Clipboard>
                    </span>
                );
            },
        },
        {
            title: lang.templateByUuid("UID:P_UBL-FE_18D8CEF6041801D2", "方案名称") /* "方案名称" */,
            dataIndex: "dataTypeName",
            sorter: "string",
            render: (value, record) => {
                return (
                    <span style={{ color: "#0033CC", cursor: "pointer" }} onClick={this.changeViewManageModalShow.bind(null, record)}>
                        {value}
                    </span>
                );
            },
        },
        {
            title: lang.templateByUuid("UID:P_UBL-FE_18D8CEF6041801D4", "触发方式") /* "触发方式" */,
            dataIndex: "startTypeInt",
            width: 100,
            render: (value, record) => triggerModeMap[value] || "",
        },
        {
            title: lang.templateByUuid("UID:P_UBL-FE_18D8CEF6041801DC", "连接配置") /* "连接配置" */,
            dataIndex: "gatewayid",
            width: 200,
        },

        {
            title: lang.templateByUuid("UID:P_UBL-FE_18D8CEF6041801E3", "启用/停用") /* "启用/停用" */,
            dataIndex: "enable",
            width: 100,
            render: this.renderChangeStatus,
        },
        {
            title: lang.templateByUuid("UID:P_UBL-FE_18D8CEF6041801E5", "运行状态") /* "运行状态" */,
            dataIndex: "taskstatus",
            width: 100,
            render: this.renderStatus,
        },
        {
            title: lang.templateByUuid("UID:P_UBL-FE_1F7D965C0560001C", "最后执行时间") /* "最后执行时间" */,
            dataIndex: "lastexectime",
            width: 160,
        },
    ];

    // 校验停用/启用
    enableStatus = async (data) => {
        const res = await autoServiceMessage({
            service: ownerService.batchChangeEnableService(data),
            success: lang.templateByUuid("UID:P_UBL-FE_1C2B23C20448000A", "操作成功") /* "操作成功" */,
        });
        if (res) {
            this.props.ownerStore.getDataSource({ ...this.searchDataVars, pageNo: 1 });
            this.setState({ selectedRowKeys: [] });
        }
    };
    // 校验批量停用/启用状态
    handleBatchSwitch = async (bool) => {
        const { selectedRowKeys = [], selectedRows } = this.state;
        if (!selectedRowKeys.length) {
            Message.destroy();
            Message.create({ content: lang.templateByUuid("UID:P_UBL-FE_1C2B23C204480009", "请先选择同步任务") /* "请先选择同步任务" */, color: "danger" });
            return;
        }
        const filteredIds = selectedRows.reduce((acc, item) => (item.enable !== bool ? acc.concat(item.id) : acc), []);
        // 校验互斥
        const data = {
            ids: selectedRowKeys,
            status: bool,
        };
        const res = await autoServiceMessage({
            service: ownerService.batchCheckEnableService(data),
            error: (err) =>
                Modal.error({
                    title: "error",
                    content: err.msg,
                    onOk: () => this.enableStatus(data),
                }),
        });
        if (res?.status === 1) {
            // 校验通过, 调用停启用接口
            this.enableStatus(data);
        }
    };

    handleSearch = (values) => {
        this.props.queryContext.updateQuery(values);
    };

    handleRowHover = (index, record) => {
        this.props.ownerStore.onTaskGridSelect([record]);
    };

    handNavBatchLog = () => {
        this.props.navigate({
            pathname: "/batch-log",
        });
    };

    //数据修复
    handNavFailCompensate = () => {
        this.props.navigate({
            pathname: "/fail-compensate",
        });
    };
    //数据修复
    handNavTiming = () => {
        this.props.navigate({
            pathname: "/timing",
        });
    };

    searchLabelCol = 100;

    handleCloseConditionModal = () => {
        this.setState({ conditionFilter: { visible: false, data: null } });
    };
    // 带有筛选条件的立即执行
    handleImExecute = async (data = {}) => {
        console.log("立即执行条件data----", data);
        const { isPull, lastsuctime, rangePicker, ...restData } = data;
        const paramData = {
            isPull: isPull,
            lastsuctime: lastsuctime,
            startTime: rangePicker?.[0],
            endTime: rangePicker?.[1],
            enableWhere: false,
            makeUp: false,
        };
        const bodyData = {
            taskIds: [this.state.conditionFilter?.data?.pk_id],
            errorDataIds: [],
            taskParams: [
                {
                    taskId: this.state.conditionFilter?.data?.pk_id,
                    taskParam: { ...restData },
                },
            ],
        };
        const res = await autoServiceMessage({
            service: immediateExecuteService(bodyData, paramData, { serviceCode: this.props.ownerState.serviceCodeDiwork }),
        });

        if (res && res.data) {
            await this.props.ownerStore.getDataSource({ ...this.searchDataVars, pageNo: 1 }).catch((err) => console.log(err));
            this.handleCloseConditionModal();
            Modal.success({
                title: lang.templateByUuid("UID:P_UBL-FE_18D8CEF6041801D3", "操作成功！") /* "操作成功！" */,
                fieldid: "************",
                content: (
                    <div>
                        <p>
                            {lang.templateByUuid("UID:P_UBL-FE_18D8CEF6041801D5", "将要执行的数据同步任务：", undefined, {
                                returnStr: true,
                            }) + res.data.viewNames || ""}
                        </p>
                    </div>
                ),
                confirmType: "one",
                okText: lang.templateByUuid("UID:P_UBL-FE_18D8CEF6041801D8", "查看执行详情") /* "查看执行详情" */,
                okCancel: true,
                onOk: () => {
                    this.props.navigate({
                        pathname: "/batch-detail",
                        search: "?batchId=" + res.data.batchId,
                    });
                },
            });
        }
    };
    handleOpenDispatchModal = async (record) => {
        const { jobName = "", dataTypeName = "", taskId } = record;
        if (record.fromType === 3) {
            // 集成流
            this.setState({
                flowSchedulerData: {
                    taskCode: jobName,
                    taskId,
                    visible: true,
                },
            });
            return;
        }
        let typeId, treeId;
        // if (record.fromType === 3) {
        //     treeId = "ssb";
        //     typeid = "db6f28db521a4c648d09101f539d57a6";
        // } else {
        //     treeId = "GZTUBL"; // 数据集成treeNode code
        //     typeId = "066518a21b7541d38138e0b8e6b3d887";
        // }
        treeId = "GZTUBL"; // 数据集成treeNode code
        typeId = "066518a21b7541d38138e0b8e6b3d887";
        const baseUrl = "/iuap-apcom-supportcenter/ucf-wh/dispatch/bpaas-dispatch-fe/index.html#/";

        const encodedDataTypeName = encodeURIComponent(dataTypeName);
        // typeId: 调度类型的id, taskCode编辑和新增都必传，对方需要根据此来判断是否调用我方close
        let iframeSrc = `${baseUrl}add/add-01?treeId=${treeId}&taskCode=${jobName}&taskName=${encodedDataTypeName}&typeId=${typeId}&locale=${window.lang.lang || "zh_cn"}`;
        if (record?.taskId) {
            //路径参数id是调度任务的id
            iframeSrc = `${baseUrl}add/${record?.taskId}?treeId=${treeId}&taskCode=${jobName}`;
        }
        this.dispatchModalRef.current.openDispatchModal(iframeSrc, "edit");
    };
    handleDispatchModalClose = () => {
        this.props.ownerStore.getDataSource({ ...this.searchDataVars, pageNo: 1 });
    };
    // 部分树做了在treeSelect的时候可以同时展开子节点,其中if判断比较复杂,抽取出来
    judgeTreeNodeSelect = (e) => {
        return (
            e &&
            (e.eventObject.target.nodeName.toLowerCase() === "span" ||
                e.eventObject.target.nodeName.toLowerCase() === "a" ||
                e.eventObject.target.nodeName.toLowerCase() === "img" ||
                Array.prototype.includes.call(e.eventObject.target.classList, "wui-icon") ||
                Array.prototype.includes.call(e.eventObject.target.classList, "iPS-custom-public-line"))
        );
    };
    // 左侧树节点选择
    handleTreeSelect = (keys, e) => {
        const { setSelectedKeys, getDataSource } = this.props.ownerStore;
        setSelectedKeys(keys, e.node.props?.["parentId"] === undefined, e.node.props?.["isSchemeApp"]);
        if (this.judgeTreeNodeSelect(e)) {
            // 判断点击的树节点有没有子节点，如果有子节点，则触发展开
            if (e.node.props?.children?.length > 0) {
                e.eventObject.currentTarget.parentElement.getElementsByClassName("wui-tree-switcher")[0].click();
            }
        }
        this.props.ownerStore.getDataSource({ ...this.searchDataVars, pageNo: 1 });
    };
    handleExpandKeys = (keys) => {
        this.props.ownerStore.changeState({
            expandedKeys: keys,
            autoExpandParent: false,
        });
    };

    handleSetTreeWidth = (size) => {
        this.setState({
            treeWidth: size,
        });
    };

    handleFlowSchedulerClose = () => {
        this.setState({
            flowSchedulerData: { taskCode: "", taskId: "", visible: false },
        });
    };
    render() {
        let { checkShow, checkLogShow, logData, viewManageModalShow, isTenantRes, conditionFilter, flowSchedulerData } = this.state;
        let { ownerState, ownerStore } = this.props;
        let { dataSource, pagination, selectedTask, selectedKeys, expandedKeys, isRoot, autoExpandParent, keywords } = ownerState;
        let rowSelection = {
            selectedRowKeys: this.state.selectedRowKeys,
            onChange: (selectedRowKeys, selectedRows) => {
                this.setState({
                    selectedRowKeys,
                    selectedRows,
                });
            },
        };
        const menu2 = (
            <Menu fieldid="a88364a6-5d9f-4f78-bc48-fcf6617f1ca6">
                <Menu.Item key="1" onClick={() => this.handleBatchSwitch(true)}>
                    {lang.templateByUuid("UID:P_UBL-FE_1C2B23C20448000B", "启用") /* "启用" */}
                </Menu.Item>
                <Menu.Item key="2" onClick={() => this.handleBatchSwitch(false)}>
                    {lang.templateByUuid("UID:P_UBL-FE_1C2B23C20448000C", "停用") /* "停用" */}
                </Menu.Item>
            </Menu>
        );
        return (
            <>
                <Layout fieldid="f730889b-781c-47d5-a103-b3dcff1afe7a" className={styles["integrate-task"]}>
                    <PageIntroduce
                        code="syncData"
                        title={lang.templateByUuid("UID:P_UBL-FE_1C081F0E05200029", "数据同步任务")}
                        descList={[
                            lang.templateByUuid("UID:P_UBL-FE_2046042005E00007","数据同步任务聚焦于集成流、集成方案的运行管理，负责统一管理、调度和执行集成流、集成方案，包括定时触发规则配置、运行状态监控、运行记录查询、问题排查等功能") /* "数据同步任务聚焦于集成流、集成方案的运行管理，负责统一管理、调度和执行集成流、集成方案，包括定时触发规则配置、运行状态监控、运行记录查询、问题排查等功能" */,
                        ]}
                        {...this.props.otherSource?.pageIntroduce}
                    />
                    <Spin
                        fieldid="d67328e8-f81a-4088-b8b4-377cce4dc220"
                        getPopupContainer={() => document.getElementById("application-tree")}
                        show={this.props.ownerState.treeLoading}
                        tip={lang.templateByUuid("UID:P_UBL-FE_1F7D965C05600019", "加载中...") /* "加载中..." */}
                    />
                    <Spliter
                        className={styles["integrate-task-content"]}
                        size={this.state.treeWidth}
                        maxSize={350}
                        minSize={0}
                        onDragMove={this.handleSetTreeWidth}
                        collapsible
                        defaultCollapsed={this.props.otherSource?.source === 3 ? true : false}
                        trigger={this.props.otherSource?.source === 3 ? null : undefined}
                        resizerable={this.props.otherSource?.source === 3 ? false : true}
                    >
                        <div className={styles["application-tree"]} id="application-tree">
                            <div className={styles["application-tree-search"]}>
                                <Input
                                    fieldid="e92a75fd-3efa-43fe-8406-d9cd3024737d"
                                    placeholder={lang.templateByUuid("UID:P_IPB-FE_1C00A89C05A80150", "请输入名称")}
                                    type="search"
                                    value={keywords}
                                    onChange={this.props.ownerStore.setKeywords}
                                    allowClear
                                />
                            </div>

                            {/* <Loading  container={true} type="line"> */}
                            <div className={styles["application-tree-data"]}>
                                {this.props.ownerState.treeSource?.length > 0 && (
                                    <Tree
                                        fieldid="4fbd9ccd-292b-4b42-94b8-56f1b74b39d0"
                                        cancelUnSelect
                                        defaultExpandAll
                                        showLine
                                        selectedKeys={selectedKeys}
                                        // expandedKeys={expandedKeys}
                                        // autoExpandParent={autoExpandParent}
                                        filterValue={keywords}
                                        optionFilterProp="composeTitle"
                                        onSelect={this.handleTreeSelect}
                                        // onExpand={this.handleExpandKeys}
                                        fieldNames={{
                                            title: "name",
                                            key: "key",
                                            children: "children",
                                            composeTitle: "composeTitle",
                                        }}
                                        treeData={this.props.ownerState.treeSource}
                                    />
                                )}
                            </div>
                        </div>
                        <div className={styles["sync-task-content"]} style={{ height: "100%" }}>
                            <SearchForm
                                ref={this.searchFormRef}
                                fieldid="824b243e-09a8-4e0f-ab2e-4c83395f06b5"
                                showSelected={false}
                                connectorsList={this.props.ownerState.connectorsList || []}
                                initData={this.props.queryContext.initData || {}}
                                onSearch={this.handleSearch}
                                onReset={this.handleSearch}
                                locale={window.lang.lang.toLowerCase() || "zh_cn"}
                                submitter={{ searchConfig: { resetText: lang.templateByUuid("UID:P_UBL-FE_18D7622804180027", "重置") } }}
                            />
                            <div className="ucg-pad-hori-lg ucg-pad-b-10  clearfix" style={{ marginTop: "10px" }}>
                                <Space fieldid="ac41abe9-f78a-4ee4-84ae-05d2fbd48121" size={8} className="ucg-float-r">
                                    <Dropdown.Button overlay={menu2}>
                                        {lang.templateByUuid("UID:P_UBL-FE_1C2B23C20448000B", "启用") /* "启用" */}
                                    </Dropdown.Button>
                                    <Button fieldid="ublinker-routes-list-components-IndexView-index-7521753-Button" onClick={this.handNavTiming}>
                                        {lang.templateByUuid("UID:P_UBL-FE_18D8CEF6041801DD", "定时执行")}
                                    </Button>

                                    <Button fieldid="0bd319a9-6dc6-4b9b-ae96-b73105106677" onClick={this.handNavBatchLog}>
                                        {lang.templateByUuid("UID:P_UBL-FE_18D8CEF6041801D1", "任务运行监控", undefined, {
                                            returnStr: true,
                                        })}
                                    </Button>
                                </Space>
                            </div>

                            <div className={styles["sync-data-table"]} ref={this.bodyRef}>
                                <DragColumnTable
                                    dragborder
                                    columns={this.columnsVar}
                                    fieldid="ublinker-routes-list-components-IndexView-index-2935327-Grid"
                                    rowKey={"pk_id"}
                                    data={dataSource?.list || []}
                                    hoverContent={this.hoverContent}
                                    scroll={{ y: this.state.size.height - 32 }}
                                    bodyStyle={{ minHeight: this.state.size.height - 32 }}
                                    rowSelection={rowSelection}
                                    autoCheckedByClickRows={false}
                                    showSorterTooltip
                                />
                            </div>
                            {pagination && (
                                <Pagination
                                    fieldid="b273b09c-c97f-4d88-be39-97b9ac2e271d"
                                    current={pagination.activePage}
                                    onChange={(a, b) => {
                                        this.searchDataVars = this.props.queryContext.query;
                                        pagination?.onPageChange({ ...this.searchDataVars, pageSize: b, pageNo: a });
                                    }}
                                    onPageSizeChange={(a, b) => {
                                        this.searchDataVars = this.props.queryContext.query;
                                        pagination?.onPageChange({ ...this.searchDataVars, pageSize: b, pageNo: 1 });
                                    }}
                                    showSizeChanger
                                    total={pagination.total}
                                    pageSize={pagination.pageSize}
                                    // style={{ position: "fixed", bottom: "5px", right: "0" }}
                                    pageSizeOptions={[...Pagination.dataNumSelect["page"]]}
                                />
                            )}
                        </div>
                    </Spliter>
                </Layout>
                <CheckModal
                    taskInfo={selectedTask}
                    show={checkShow}
                    onCancel={this.changeCheckShow}
                    onOk={this.handleCheckOk}
                    onCheck={this.handleCheck}
                    ownerStore={ownerStore}
                    ownerState={ownerState}
                />
                <CheckLogModal taskInfo={selectedTask} show={checkLogShow} logData={logData} onCancel={this.changeCheckLogClose} />
                {/* <CreateTimingModal show={createTimingShow} taskInfo={selectedTask} onCancel={this.changeCreateTimingShow} onOk={this.createTimingOk} /> */}
                <ViewManageModal
                    show={viewManageModalShow}
                    selectedTask={selectedTask}
                    onCancel={() => this.handleCloseViewManageModal(selectedTask)}
                    // onOk={this.submitMakeUp}
                    ownerStore={ownerStore}
                    ownerState={ownerState}
                    //连接配置用
                    res={isTenantRes}
                    oldDataParam={selectedTask && selectedTask.startSchemeId ? {} : { fromType: 0 }} //点开网关弹框出来之后有个请求，如果是老数据给请求加个参数
                />
                {conditionFilter?.visible ? (
                    <ConditionFilterModal
                        visible={conditionFilter?.visible}
                        data={conditionFilter?.data}
                        onCancel={this.handleCloseConditionModal}
                        onOk={this.handleImExecute}
                        ownerState={ownerState}
                        ownerStore={ownerStore}
                    />
                ) : null}
                <DispatchModal ref={this.dispatchModalRef} onClose={this.handleDispatchModalClose} />

                {flowSchedulerData?.visible && <FlowScheduler {...flowSchedulerData} onCancel={this.handleFlowSchedulerClose} />}
            </>
        );
    }
}

export default IndexView;
